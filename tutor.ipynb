{"cells": [{"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["import os\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "API_KEY = os.getenv('API_KEY')\n", "DATABASE_PATH = os.getenv('DATABASE_PATH')\n", "CHROMA_PERSIST_DIR = os.getenv('CHROMA_PERSIST_DIR')\n", "EMBEDDING_MODEL = os.getenv('EMBEDDING_MODEL')\n", "LLM_MODEL = os.getenv('LLM_MODEL')\n", "LLM_TEMPERATURE = os.getenv('LLM_TEMPERATURE')\n", "CHUNK_SIZE = os.getenv('CHUNK_SIZE')\n", "CHUNK_OVERLAP = os.getenv('CHUNK_OVERLAP')\n", "RETRIEVAL_K = os.getenv('RETRIEVAL_K')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sqlite3\n", "import pandas as pd\n", "import numpy as np\n", "import codecs\n", "import re\n", "import unicodedata\n", "from typing import List, Dict, Any\n", "from dotenv import load_dotenv\n", "\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain.embeddings.base import Embeddings\n", "from langchain.schema import Document\n", "\n", "# Sentence Transformers\n", "from sentence_transformers import SentenceTransformer\n", "\n", "# ChromaDB\n", "import chromadb\n", "from chromadb.config import Settings\n", "from langchain_community.vectorstores import Chroma"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["import shutil\n", "shutil.rmtree(\"your_chroma_persist_dir\", ignore_errors=True)\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["class AITutor:\n", "    \"\"\"RAG-based AI Tutor for subjects\"\"\"\n", "\n", "    def __init__(\n", "        self, db_path: str = DATABASE_PATH, \n", "        chroma_persist_dir: str = CHROMA_PERSIST_DIR,\n", "        embedding_model: str = EMBEDDING_MODEL,\n", "        chunk_size: int = CHUNK_SIZE,\n", "        chunk_overlap: int = CHUNK_OVERLAP,\n", "        ):\n", "\n", "        self.db_path = db_path\n", "        self.chroma_persist_dir = chroma_persist_dir\n", "        self.embedding_model = embedding_model\n", "        self.chunk_size = int(chunk_size)\n", "        self.chunk_overlap = int(chunk_overlap)\n", "        self.embedding = None\n", "        self.vectorstore = None\n", "\n", "    def clean_bangla_latex_text(self, text):\n", "        \"\"\"Clean and normalize Bangla/English text with LaTeX content\"\"\"\n", "        if not isinstance(text, str):\n", "            return str(text)\n", "        try:\n", "            text = text.replace('\\\\n', ' ')\n", "            text = text.replace('\\n', ' ')\n", "            try:\n", "                if '\\\\u' in text or '\\\\x' in text:\n", "                    text = codecs.decode(text, 'unicode_escape')\n", "            except (UnicodeDecodeError, UnicodeError):\n", "                pass\n", "            text = unicodedata.normalize('NFC', text)\n", "            text = re.sub(r'\\s+', ' ', text)\n", "            text = text.encode('utf-8', errors='ignore').decode('utf-8')\n", "            return text.strip()    \n", "        except Exception as e:\n", "            try:\n", "                cleaned = re.sub(r'\\s+', ' ', str(text))\n", "                return cleaned.strip()\n", "            except:\n", "                return str(text)\n", "\n", "    def load_and_preprocess_data(self):\n", "        \"\"\"Load data from SQLite database and preprocess it\"\"\"\n", "        try:\n", "            if not os.path.exists(self.db_path):\n", "                raise FileNotFoundError(f\"Database file not found: {self.db_path}\")\n", "            conn = sqlite3.connect(self.db_path)\n", "            query = \"\"\"\n", "            SELECT book_name, author_name, page_number, text_content FROM book_images;\n", "            \"\"\"\n", "            df = pd.read_sql_query(query, conn)\n", "            conn.close()\n", "            if len(df) == 0:\n", "                raise ValueError(\"No data found in the database\")\n", "\n", "            df['text_content'] = df['text_content'].apply(self.clean_bangla_latex_text)\n", "            df = df[df['text_content'].str.strip() != '']\n", "            return df  \n", "        except Exception as e:\n", "            print(f\"Error loading data: {e}\")\n", "            raise \n", "\n", "    def create_documents(self, df: pd.DataFrame) -> List[Document]:\n", "        \"\"\"Create document chunks from DataFrame\"\"\"\n", "        try:\n", "            text_splitter = RecursiveCharacterTextSplitter(\n", "                chunk_size=self.chunk_size,\n", "                chunk_overlap=self.chunk_overlap\n", "            )\n", "            \n", "            documents = []\n", "            \n", "            for _, row in df.iterrows():\n", "                # Create metadata for each document\n", "                metadata = {\n", "                    'book_name': row['book_name'],\n", "                    'author_name': row['author_name']\n", "                }\n", "                \n", "                # Split the text into chunks\n", "                chunks = text_splitter.split_text(row['text_content'])\n", "                \n", "                # Create Document objects for each chunk\n", "                for i, chunk in enumerate(chunks):\n", "                    doc_metadata = metadata.copy()\n", "                    doc_metadata['chunk_id'] = i\n", "                    documents.append(Document(page_content=chunk, metadata=doc_metadata))\n", "            \n", "            print(f\"Created {len(documents)} document chunks\")\n", "            return documents\n", "            \n", "        except Exception as e:\n", "            print(f\"Error creating documents: {e}\")\n", "            raise\n", "    \n", "    def initialize_embeddings(self):\n", "        \"\"\"Initialize the sentence transformer embedding model\"\"\"\n", "        try:\n", "            print(f\"Loading embedding model: {self.embedding_model}\")\n", "            self.embedding = SentenceTransformer(self.embedding_model)\n", "            print(\"Embedding model loaded successfully\")\n", "        except Exception as e:\n", "            print(f\"Error loading embedding model: {e}\")\n", "            raise\n", "    \n", "    def create_vector_store(self, documents: List[Document]):\n", "        \"\"\"Create ChromaDB vector store from documents\"\"\"\n", "        try:\n", "            if self.embedding is None:\n", "                self.initialize_embeddings()\n", "            \n", "            print(f\"Creating vector store with {len(documents)} documents...\")\n", "            \n", "            # Create a custom embedding class for LangChain compatibility\n", "            class SentenceTransformerEmbeddings(Embeddings):\n", "                def __init__(self, model):\n", "                    self.model = model\n", "                \n", "                def embed_documents(self, texts: List[str]) -> List[List[float]]:\n", "                    return self.model.encode(texts).tolist()\n", "                \n", "                def embed_query(self, text: str) -> List[float]:\n", "                    return self.model.encode([text])[0].tolist()\n", "            \n", "            # Create the embedding wrapper\n", "            embedding_wrapper = SentenceTransformerEmbeddings(self.embedding)\n", "            \n", "            # Create ChromaDB vector store\n", "            self.vectorstore = Chroma.from_documents(\n", "                documents=documents,\n", "                embedding=embedding_wrapper,\n", "                persist_directory=self.chroma_persist_dir\n", "            )\n", "            \n", "            print(f\"Vector store created successfully with {len(documents)} documents\")\n", "            print(f\"Persisted to: {self.chroma_persist_dir}\")\n", "            \n", "        except Exception as e:\n", "            print(f\"Error creating vector store: {e}\")\n", "            raise\n", "    \n", "    def build_vector_database(self):\n", "        \"\"\"Main method to build the complete vector database\"\"\"\n", "        try:\n", "            print(\"Starting vector database creation...\")\n", "            \n", "            # Step 1: Load and preprocess data\n", "            print(\"\\n1. Loading and preprocessing data...\")\n", "            df = self.load_and_preprocess_data()\n", "            print(f\"Loaded {len(df)} records from database\")\n", "            \n", "            # Step 2: Create document chunks\n", "            print(\"\\n2. Creating document chunks...\")\n", "            documents = self.create_documents(df)\n", "            \n", "            # Step 3: Initialize embeddings\n", "            print(\"\\n3. Initializing embedding model...\")\n", "            self.initialize_embeddings()\n", "            \n", "            # Step 4: Create vector store\n", "            print(\"\\n4. Creating vector store...\")\n", "            self.create_vector_store(documents)\n", "            \n", "            print(\"\\n✅ Vector database creation completed successfully!\")\n", "            return self.vectorstore\n", "            \n", "        except Exception as e:\n", "            print(f\"\\n❌ Error in vector database creation: {e}\")\n", "            raise\n", "\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting vector database creation...\n", "\n", "1. Loading and preprocessing data...\n", "Loaded 5150 records from database\n", "\n", "2. Creating document chunks...\n", "Created 37831 document chunks\n", "\n", "3. Initializing embedding model...\n", "Loading embedding model: distiluse-base-multilingual-cased-v2\n", "Embedding model loaded successfully\n", "\n", "4. Creating vector store...\n", "Creating vector store with 37831 documents...\n", "Vector store created successfully with 37831 documents\n", "Persisted to: ./chroma_db\n", "\n", "✅ Vector database creation completed successfully!\n", "\n", "🔍 Testing vector store with a sample query...\n", "\n", "Found 3 similar documents for query: 'physics'\n", "\n", "Result 1:\n", "Book: Physics 1st Paper\n", "Author: <PERSON><PERSON>\n", "Content preview: ঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃঃ...\n", "\n", "Result 2:\n", "Book: Physics 1st Paper\n", "Author: <PERSON><PERSON>\n", "Content preview: \\times 9.8}{200} = 196~\\text{kg/sec} \\] এখানে, \\[ \\begin{align*} m &= 4000 \\text{ kg} \\\\ v &= 200~\\text{ms}^{-1} \\\\ g &= 9.8~\\text{ms}^{-2} \\end{align*} \\] ৪.৭ নিউটনের গতিসূত্রের অবদান Contribution of...\n", "\n", "Result 3:\n", "Book: Physics 1st Paper\n", "Author: <PERSON><PERSON>\n", "Content preview: \\times 9.8}{200} = 196~\\text{kg/sec} \\] এখানে, \\[ \\begin{align*} m &= 4000 \\text{ kg} \\\\ v &= 200~\\text{ms}^{-1} \\\\ g &= 9.8~\\text{ms}^{-2} \\end{align*} \\] ৪.৭ নিউটনের গতিসূত্রের অবদান Contribution of...\n", "\n", "✅ Vector database is ready for use!\n"]}], "source": ["# Create and run the AI Tutor to build vector database\n", "if __name__ == \"__main__\":\n", "    try:\n", "        # Initialize the AI Tutor\n", "        tutor = AITutor()\n", "        \n", "        # Build the vector database\n", "        vectorstore = tutor.build_vector_database()\n", "        \n", "        # Test the vector store with a simple query\n", "        print(\"\\n🔍 Testing vector store with a sample query...\")\n", "        test_query = \"physics\"\n", "        results = vectorstore.similarity_search(test_query, k=3)\n", "        \n", "        print(f\"\\nFound {len(results)} similar documents for query: '{test_query}'\")\n", "        for i, doc in enumerate(results, 1):\n", "            print(f\"\\nResult {i}:\")\n", "            print(f\"Book: {doc.metadata.get('book_name', 'Unknown')}\")\n", "            print(f\"Author: {doc.metadata.get('author_name', 'Unknown')}\")\n", "            print(f\"Content preview: {doc.page_content[:200]}...\")\n", "        \n", "        print(\"\\n✅ Vector database is ready for use!\")\n", "        \n", "    except Exception as e:\n", "        print(f\"\\n❌ Error: {e}\")\n", "        import traceback\n", "        traceback.print_exc()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Vector Database Creation Complete\n", "\n", "This notebook creates a vector database from your text data using:\n", "\n", "1. **Text Chunking**: Uses RecursiveCharacterTextSplitter to break text into manageable chunks\n", "2. **Embeddings**: Uses SentenceTransformer with the multilingual model for text embeddings\n", "3. **Vector Storage**: Stores embeddings in ChromaDB for efficient similarity search\n", "\n", "The vector database is now ready to be used with your RAG system in a separate Python file."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 2}