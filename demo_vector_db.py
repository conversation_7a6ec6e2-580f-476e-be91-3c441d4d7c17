#!/usr/bin/env python3
"""
Vector Database Demo
===================

This script demonstrates that the vector database is working correctly
for AI Tutor applications using the existing ChromaDB from tutor.ipynb.

Usage:
    python demo_vector_db.py
"""

import os
import sys

def test_chromadb():
    """Test the existing ChromaDB vector database"""
    print("🧪 Testing ChromaDB Vector Database...")
    print("=" * 50)
    
    try:
        # Import required libraries
        from langchain_community.vectorstores import Chroma
        from sentence_transformers import SentenceTransformer
        from langchain.embeddings.base import Embeddings
        from typing import List
        
        # Create embedding wrapper (same as in notebook)
        class SentenceTransformerEmbeddings(Embeddings):
            def __init__(self, model_name="distiluse-base-multilingual-cased-v2"):
                self.model = SentenceTransformer(model_name)
            
            def embed_documents(self, texts: List[str]) -> List[List[float]]:
                return self.model.encode(texts).tolist()
            
            def embed_query(self, text: str) -> List[float]:
                return self.model.encode([text])[0].tolist()
        
        print("📂 Loading existing ChromaDB vector database...")
        
        # Check if ChromaDB exists
        if not os.path.exists("./chroma_db"):
            print("❌ ChromaDB not found! Run tutor.ipynb first.")
            return False
        
        # Initialize embeddings
        print("🧠 Loading embedding model...")
        embedding_wrapper = SentenceTransformerEmbeddings()
        
        # Load ChromaDB vector store
        vectorstore = Chroma(
            persist_directory="./chroma_db",
            embedding_function=embedding_wrapper
        )
        
        print("✅ ChromaDB loaded successfully!")
        
        # Test queries
        test_queries = [
            "physics",
            "জৈব যৌগ",
            "mathematics", 
            "নিউটনের সূত্র"
        ]
        
        print(f"\n🔍 Testing {len(test_queries)} sample queries...")
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n{i}. Query: '{query}'")
            
            try:
                # Perform similarity search
                results = vectorstore.similarity_search(query, k=3)
                print(f"   ✅ Found {len(results)} results")
                
                if results:
                    # Show first result details
                    first = results[0]
                    print(f"   📚 Book: {first.metadata.get('book_name', 'Unknown')}")
                    print(f"   👤 Author: {first.metadata.get('author_name', 'Unknown')}")
                    print(f"   📄 Content: {first.page_content[:80]}...")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        print("\n" + "=" * 50)
        print("✅ ChromaDB Vector Database is Working!")
        print("🚀 Ready for AI Tutor integration!")
        
        return True
        
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("   Install with: pip install sentence-transformers chromadb langchain-community")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def show_database_info():
    """Show information about available vector databases"""
    print("\n📊 Vector Database Status:")
    print("-" * 30)
    
    # Check ChromaDB
    if os.path.exists("./chroma_db"):
        print("✅ ChromaDB: Available (from tutor.ipynb)")
        print("   📁 Location: ./chroma_db")
        print("   🔧 Type: ChromaDB with SentenceTransformer embeddings")
    else:
        print("❌ ChromaDB: Not found")
    
    # Check FAISS DB
    if os.path.exists("./faiss_db"):
        print("✅ FAISS DB: Available (from walid.py)")
        print("   📁 Location: ./faiss_db")
        print("   🔧 Type: FAISS with HuggingFace embeddings")
    else:
        print("⚠️  FAISS DB: Not created yet")
        print("   💡 Run build_vector_db.py to create FAISS database")

def main():
    """Main function"""
    print("🎯 Vector Database Demo for AI Tutor")
    print("=" * 50)
    
    # Show database status
    show_database_info()
    
    # Test ChromaDB
    print("\n" + "=" * 50)
    success = test_chromadb()
    
    if success:
        print("\n🎉 Vector Database Demo Complete!")
        print("\n📋 Summary:")
        print("   ✅ ChromaDB vector database is working")
        print("   ✅ Similarity search is functional")
        print("   ✅ Ready for AI Tutor integration")
        print("\n💡 Next Steps:")
        print("   1. Use ChromaDB for immediate AI Tutor development")
        print("   2. Optionally create FAISS database with walid.py")
        print("   3. Integrate with your AI/LLM pipeline")
    else:
        print("\n❌ Vector Database Demo Failed!")
        print("   Please check dependencies and database files.")

if __name__ == "__main__":
    main()
