# Core dependencies
pandas>=1.5.0
numpy>=1.21.0

# Document processing
PyPDF2
pytesseract
pillow

# Embeddings and ML
sentence-transformers>=2.2.0

# Vector database
chromadb>=0.4.0

# Streamlit for UI
streamlit>=1.28.0

# LangChain dependencies
langchain>=0.1.0
langchain-community>=0.0.10

# Environment and utilities
python-dotenv>=1.0.0

# Hugging Face dependencies
transformers>=4.37.0
torch>=2.1.0
bitsandbytes>=0.41.0
accelerate>=0.26.0

# Additional dependencies that might be needed
requests>=2.28.0
numpy>=1.21.0
scipy>=1.9.0

# For better performance
faiss-cpu>=1.7.0

# Optional: for better text processing
nltk>=3.8
spacy>=3.4.0