# Import necessary libraries
import os
from dotenv import load_dotenv
from langchain_community.vectorstores import Chroma
from langchain.embeddings.base import Embeddings
from langchain.chains import RetrievalQA
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.prompts import PromptTemplate
from sentence_transformers import SentenceTransformer
from typing import List

# Load environment variables
load_dotenv()

# Get API key from environment
GOOGLE_API_KEY = os.getenv('API_KEY')
CHROMA_PERSIST_DIR = os.getenv('CHROMA_PERSIST_DIR')
EMBEDDING_MODEL = os.getenv('EMBEDDING_MODEL')
LLM_MODEL = os.getenv('LLM_MODEL')
LLM_TEMPERATURE = float(os.getenv('LLM_TEMPERATURE'))
RETRIEVAL_K = int(os.getenv('RETRIEVAL_K'))

print('Environment variables loaded successfully!')


# Custom embedding class for SentenceTransformer compatibility
class SentenceTransformerEmbeddings(Embeddings):
    def __init__(self, model_name: str = EMBEDDING_MODEL):
        self.model_name = model_name
        self.model = SentenceTransformer(model_name)
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        embeddings = self.model.encode(texts, convert_to_tensor=False)
        return embeddings.tolist()
    
    def embed_query(self, text: str) -> List[float]:
        embedding = self.model.encode([text], convert_to_tensor=False)
        return embedding[0].tolist()

# Initialize embedding model
embedding_model = SentenceTransformerEmbeddings(model_name=EMBEDDING_MODEL)
print('Embedding model loaded successfully!')


# Load existing ChromaDB vector store
vectordb = Chroma(
    persist_directory=CHROMA_PERSIST_DIR,
    embedding_function=embedding_model,
    collection_name='physics_tutor'
)

# Create retriever
retriever = vectordb.as_retriever(
    search_type='similarity',
    search_kwargs={'k': RETRIEVAL_K}
)

print(f'Vector store loaded with collection: physics_tutor')
print(f'Retriever configured to return top {RETRIEVAL_K} results')


# Initialize Gemini LLM
llm = ChatGoogleGenerativeAI(
    model=LLM_MODEL,
    google_api_key=GOOGLE_API_KEY,
    temperature=LLM_TEMPERATURE
)

print(f'LLM initialized: {LLM_MODEL} with temperature {LLM_TEMPERATURE}')


# Create a strong system prompt for RAG
system_prompt = """You are an expert Physics tutor with access to a comprehensive physics textbook database. 
Your primary responsibility is to provide accurate, educational responses based on the retrieved context from the physics textbook.

INSTRUCTIONS:
1. **PRIMARY SOURCE**: Always prioritize information from the retrieved context (physics textbook database) when available.
2. **CONTEXT-BASED ANSWERS**: If the retrieved context contains relevant information, base your answer primarily on that content.
3. **SUPPLEMENTARY KNOWLEDGE**: Only use your general knowledge to supplement or clarify concepts when the retrieved context is insufficient or unclear.
4. **ACCURACY FIRST**: Ensure all physics concepts, formulas, and explanations are scientifically accurate.
5. **EDUCATIONAL APPROACH**: Explain concepts clearly, step-by-step, suitable for students learning physics.
6. **MULTILINGUAL SUPPORT**: Handle both English and Bengali (বাংলা) questions appropriately.
7. **SOURCE ATTRIBUTION**: When using retrieved context, mention that the information comes from the textbook.
8. **ADMIT LIMITATIONS**: If neither the retrieved context nor your knowledge can adequately answer the question, clearly state this.

RESPONSE FORMAT:
- Start with the most relevant information from the retrieved context
- Provide clear explanations with examples when helpful
- Include relevant formulas or equations when applicable
- End with a summary or key takeaway

Retrieved Context: {context}

Student Question: {question}

Your Response:"""

# Create prompt template
prompt_template = PromptTemplate(
    input_variables=['context', 'question'],
    template=system_prompt
)

print('System prompt created successfully!')


# Build RetrievalQA chain
qa_chain = RetrievalQA.from_chain_type(
    llm=llm,
    chain_type='stuff',
    retriever=retriever,
    chain_type_kwargs={'prompt': prompt_template},
    return_source_documents=True
)

print('RAG chain created successfully!')


# Function to ask questions with detailed response
def ask_physics_question(question: str):
    """
    Ask a physics question and get a comprehensive answer
    using RAG (Retrieval-Augmented Generation)
    """
    try:
        # Get response from RAG chain
        result = qa_chain({'query': question})
        
        # Extract answer and source documents
        answer = result['result']
        source_docs = result['source_documents']
        
        # Display results
        print('=' * 80)
        print(f'QUESTION: {question}')
        print('=' * 80)
        print('ANSWER:')
        print(answer)
        print('\n' + '=' * 80)
        print(f'SOURCE DOCUMENTS ({len(source_docs)} found):')
        print('=' * 80)
        
        for i, doc in enumerate(source_docs, 1):
            print(f'Source {i}:')
            print(f'Book: {doc.metadata.get("book_name", "Unknown")}')
            print(f'Author: {doc.metadata.get("author_name", "Unknown")}')
            print(f'Page: {doc.metadata.get("page_number", "Unknown")}')
            print(f'Content: {doc.page_content[:200]}...')
            print('-' * 40)
        
        return answer, source_docs
        
    except Exception as e:
        print(f'Error occurred: {str(e)}')
        return None, None

print('Question function defined successfully!')


# Test the RAG system with sample questions

# Test Question 1: Physics concept
question1 = "What is Newton's first law of motion?"
print('Testing Question 1...')
answer1, sources1 = ask_physics_question(question1)


# Test Question 2: Bengali physics question
question2 = "ভেক্টর কি? ভেক্টরের বৈশিষ্ট্য ব্যাখ্যা করো।"
print('\nTesting Question 2 (Bengali)...')
answer2, sources2 = ask_physics_question(question2)


# Test Question 3: Mathematical physics
question3 = "Explain the concept of relative velocity with examples."
print('\nTesting Question 3...')
answer3, sources3 = ask_physics_question(question3)


# Interactive question function
def interactive_physics_tutor():
    """
    Interactive physics tutor session
    """
    print('\n' + '=' * 60)
    print('🧠 INTERACTIVE PHYSICS TUTOR')
    print('=' * 60)
    print('Ask any physics question in English or Bengali!')
    print('Type "quit" to exit.')
    print('=' * 60)
    
    while True:
        question = input('\n🔬 Your Question: ').strip()
        
        if question.lower() in ['quit', 'exit', 'q']:
            print('\n👋 Thank you for using the Physics Tutor!')
            break
        
        if not question:
            print('Please enter a valid question.')
            continue
        
        ask_physics_question(question)

print('Interactive tutor function ready!')
print('\nTo start interactive session, run: interactive_physics_tutor()')
