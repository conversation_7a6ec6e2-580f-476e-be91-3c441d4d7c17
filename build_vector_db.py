#!/usr/bin/env python3
"""
Vector Database Builder for AI Tutor
=====================================

This script builds and saves a FAISS vector database from the SQLite database
containing book content. Similar to tutor.ipynb but using FAISS instead of ChromaDB.

Usage:
    python build_vector_db.py

The script will:
1. Load documents from the SQLite database
2. Split them into chunks
3. Create embeddings using a multilingual model
4. Build a FAISS vector store
5. Save the vector store to disk for future use
"""

import os
import sys
from walid import BookSearchEngine

def main():
    """Main function to build and save vector database"""
    print("🚀 Starting Vector Database Creation...")
    print("=" * 50)
    
    try:
        # Initialize the search engine with custom persist directory
        faiss_persist_dir = "./faiss_db"
        search_engine = BookSearchEngine(
            db_path="physics_book.db",
            faiss_persist_dir=faiss_persist_dir
        )
        
        print(f"📁 Vector database will be saved to: {faiss_persist_dir}")
        print()
        
        # Check if vector database already exists
        if os.path.exists(faiss_persist_dir):
            response = input("⚠️  Vector database already exists. Rebuild? (y/N): ").strip().lower()
            if response not in ['y', 'yes']:
                print("✅ Using existing vector database.")
                # Test loading existing database
                if search_engine.load_vector_store():
                    print("🔍 Testing existing vector store...")
                    test_query = "physics"
                    results = search_engine.search_with_context(test_query, k=3)
                    print(f"Found {len(results)} results for test query: '{test_query}'")
                    print("✅ Existing vector database is working correctly!")
                    return
                else:
                    print("❌ Failed to load existing database. Will rebuild...")
        
        # Build and save the vector database
        print("🔨 Building vector database...")
        vectorstore = search_engine.build_and_save_vector_database()
        
        # Test the vector store with a sample query
        print("\n🔍 Testing vector store with sample queries...")
        test_queries = ["physics", "জৈব যৌগ", "mathematics"]
        
        for query in test_queries:
            print(f"\n📝 Testing query: '{query}'")
            try:
                results = search_engine.search_with_context(query, k=3)
                print(f"   ✅ Found {len(results)} results")
                
                if results:
                    # Show first result
                    first_result = results[0]
                    print(f"   📖 Sample result: Page {first_result['page_number']} from {first_result['book_name']}")
                    print(f"   📄 Content preview: {first_result['content'][:100]}...")
                
            except Exception as e:
                print(f"   ❌ Error testing query '{query}': {e}")
        
        print("\n" + "=" * 50)
        print("✅ Vector Database Creation Complete!")
        print(f"📁 Database saved to: {faiss_persist_dir}")
        print("🚀 Ready for use with AI Tutor!")
        
        # Show database statistics
        print("\n📊 Database Statistics:")
        print(f"   📚 Total documents: {len(search_engine.documents)}")
        print(f"   📄 Total chunks: {len(search_engine.chunks)}")
        print(f"   🧠 Embedding model: paraphrase-multilingual-mpnet-base-v2")
        print(f"   💾 Storage format: FAISS")
        
    except FileNotFoundError as e:
        print(f"❌ Database file not found: {e}")
        print("   Make sure 'physics_book.db' exists in the current directory.")
        sys.exit(1)
        
    except Exception as e:
        print(f"❌ Error during vector database creation: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
