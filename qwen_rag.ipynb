{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import os\n", "from dotenv import load_dotenv\n", "from langchain_community.vectorstores import Chroma\n", "from langchain.embeddings.base import Embeddings\n", "from langchain.chains import RetrievalQA\n", "from langchain_community.llms import HuggingFacePipeline\n", "from langchain.prompts import PromptTemplate\n", "from sentence_transformers import SentenceTransformer\n", "from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig, pipeline\n", "from typing import List\n", "\n", "# Load environment variables\n", "load_dotenv()\n", "\n", "# Environment variables\n", "CHROMA_PERSIST_DIR = os.getenv('CHROMA_PERSIST_DIR', './chroma_db')\n", "EMBEDDING_MODEL = os.getenv('EMBEDDING_MODEL', 'distiluse-base-multilingual-cased-v2')\n", "RETRIEVAL_K = int(os.getenv('RETRIEVAL_K', '5'))\n", "\n", "# Qwen model configuration\n", "model_name = \"Qwen/Qwen2.5-7B-Instruct\"\n", "bnb_config = BitsAndBytesConfig(load_in_4bit=True)\n", "\n", "# Initialize tokenizer and model\n", "tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "model = AutoModelForCausalLM.from_pretrained(\n", "    model_name,\n", "    quantization_config=bnb_config,\n", "    device_map=\"auto\",\n", "    torch_dtype=\"auto\"\n", ")\n", "\n", "print('Environment variables and Qwen model loaded successfully!')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Custom embedding class for SentenceTransformer compatibility\n", "class SentenceTransformerEmbeddings(Embeddings):\n", "    def __init__(self, model_name: str = EMBEDDING_MODEL):\n", "        self.model_name = model_name\n", "        self.model = SentenceTransformer(model_name)\n", "    \n", "    def embed_documents(self, texts: List[str]) -> List[List[float]]:\n", "        embeddings = self.model.encode(texts, convert_to_tensor=False)\n", "        return embeddings.tolist()\n", "    \n", "    def embed_query(self, text: str) -> List[float]:\n", "        embedding = self.model.encode([text], convert_to_tensor=False)\n", "        return embedding[0].tolist()\n", "\n", "# Initialize embedding model\n", "embedding_model = SentenceTransformerEmbeddings(model_name=EMBEDDING_MODEL)\n", "print('Embedding model loaded successfully!')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load existing ChromaDB vector store\n", "vectordb = Chroma(\n", "    persist_directory=CHROMA_PERSIST_DIR,\n", "    embedding_function=embedding_model,\n", "    collection_name='physics_tutor'\n", ")\n", "\n", "# Create retriever\n", "retriever = vectordb.as_retriever(\n", "    search_type='similarity',\n", "    search_kwargs={'k': RETRIEVAL_K}\n", ")\n", "\n", "print(f'Vector store loaded with collection: physics_tutor')\n", "print(f'Retriever configured to return top {RETRIEVAL_K} results')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create HuggingFace pipeline\n", "pipe = pipeline(\n", "    \"text-generation\",\n", "    model=model,\n", "    tokenizer=tokenizer,\n", "    max_new_tokens=512,\n", "    temperature=0.3,\n", "    top_p=0.95,\n", "    repetition_penalty=1.15\n", ")\n", "\n", "# Initialize LangChain HF Pipeline\n", "llm = HuggingFacePipeline(pipeline=pipe)\n", "\n", "print('Qwen LLM pipeline initialized successfully!')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a strong system prompt for RAG\n", "system_prompt = \"\"\"You are an expert Physics tutor with access to a comprehensive physics textbook database. \n", "Your primary responsibility is to provide accurate, educational responses based on the retrieved context from the physics textbook.\n", "\n", "INSTRUCTIONS:\n", "1. **PRIMARY SOURCE**: Always prioritize information from the retrieved context (physics textbook database) when available.\n", "2. **CONTEXT-BASED ANSWERS**: If the retrieved context contains relevant information, base your answer primarily on that content.\n", "3. **SUPPLEMENTARY KNOWLEDGE**: Only use your general knowledge to supplement or clarify concepts when the retrieved context is insufficient or unclear.\n", "4. **ACCURACY FIRST**: Ensure all physics concepts, formulas, and explanations are scientifically accurate.\n", "5. **EDUCATIONAL APPROACH**: Explain concepts clearly, step-by-step, suitable for students learning physics.\n", "6. **MULTILINGUAL SUPPORT**: <PERSON>le both English and Bengali (বাংলা) questions appropriately.\n", "7. **SOURCE ATTRIBUTION**: When using retrieved context, mention that the information comes from the textbook.\n", "8. **ADMIT LIMITATIONS**: If neither the retrieved context nor your knowledge can adequately answer the question, clearly state this.\n", "\n", "RESPONSE FORMAT:\n", "- Start with the most relevant information from the retrieved context\n", "- Provide clear explanations with examples when helpful\n", "- Include relevant formulas or equations when applicable\n", "- End with a summary or key takeaway\n", "\n", "Retrieved Context: {context}\n", "\n", "Student Question: {question}\n", "\n", "Your Response:\"\"\"\n", "\n", "# Create prompt template\n", "prompt_template = PromptTemplate(\n", "    input_variables=['context', 'question'],\n", "    template=system_prompt\n", ")\n", "\n", "print('System prompt created successfully!')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Build RetrievalQA chain\n", "qa_chain = RetrievalQA.from_chain_type(\n", "    llm=llm,\n", "    chain_type='stuff',\n", "    retriever=retriever,\n", "    chain_type_kwargs={'prompt': prompt_template},\n", "    return_source_documents=True\n", ")\n", "\n", "print('RAG chain created successfully!')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Function to ask questions with detailed response\n", "def ask_physics_question(question: str):\n", "    \"\"\"\n", "    Ask a physics question and get a comprehensive answer\n", "    using RAG (Retrieval-Augmented Generation)\n", "    \"\"\"\n", "    try:\n", "        # Get response from RAG chain\n", "        result = qa_chain({'query': question})\n", "        \n", "        # Extract answer and source documents\n", "        answer = result['result']\n", "        source_docs = result['source_documents']\n", "        \n", "        # Display results\n", "        print('=' * 80)\n", "        print(f'QUESTION: {question}')\n", "        print('=' * 80)\n", "        print('ANSWER:')\n", "        print(answer)\n", "        print('\\n' + '=' * 80)\n", "        print(f'SOURCE DOCUMENTS ({len(source_docs)} found):')\n", "        print('=' * 80)\n", "        \n", "        for i, doc in enumerate(source_docs, 1):\n", "            print(f'Source {i}:')\n", "            print(f'Book: {doc.metadata.get(\"book_name\", \"Unknown\")}')\n", "            print(f'Author: {doc.metadata.get(\"author_name\", \"Unknown\")}')\n", "            print(f'Page: {doc.metadata.get(\"page_number\", \"Unknown\")}')\n", "            print(f'Content: {doc.page_content[:200]}...')\n", "            print('-' * 40)\n", "        \n", "        return answer, source_docs\n", "        \n", "    except Exception as e:\n", "        print(f'Error occurred: {str(e)}')\n", "        return None, None\n", "\n", "print('Question function defined successfully!')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test the RAG system with sample questions\n", "\n", "# Test Question 1: Physics concept\n", "question1 = \"What is <PERSON>'s first law of motion?\"\n", "print('Testing Question 1...')\n", "answer1, sources1 = ask_physics_question(question1)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test Question 2: Bengali physics question\n", "question2 = \"ভেক্টর কি? ভেক্টরের বৈশিষ্ট্য ব্যাখ্যা করো।\"\n", "print('\\nTesting Question 2 (Bengali)...')\n", "answer2, sources2 = ask_physics_question(question2)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test Question 3: Mathematical physics\n", "question3 = \"Explain the concept of relative velocity with examples.\"\n", "print('\\nTesting Question 3...')\n", "answer3, sources3 = ask_physics_question(question3)\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 2}