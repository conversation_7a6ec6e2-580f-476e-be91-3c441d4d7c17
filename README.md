# 🧠 AI Physics Tutor

A comprehensive RAG (Retrieval-Augmented Generation) based AI tutor system for Physics education, supporting both English and Bengali languages. Built with LangChain, ChromaDB, Gemini AI, and Streamlit.

## 🌟 Features

- **📚 RAG-based Learning**: Uses physics textbook database for accurate, contextual responses
- **🌐 Multilingual Support**: Supports both English and Bengali (বাংলা)
- **🧠 Memory**: Maintains conversation history for contextual understanding
- **⚡ Real-time Responses**: Fast and efficient question answering
- **🎨 Modern UI**: ChatGPT-like interface built with Streamlit
- **🔍 Smart Retrieval**: Uses ChromaDB for efficient vector search
- **📊 Source Attribution**: Shows relevant textbook sources for answers

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Streamlit UI  │────│   AI Tutor Core  │────│  Physics DB     │
│   (Frontend)    │    │   (RAG System)   │    │  (SQLite)       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                       ┌────────┴────────┐
                       │                 │
                ┌──────▼──────┐   ┌──────▼──────┐
                │  ChromaDB   │   │   Gemini    │
                │ (Vectors)   │   │    (LLM)    │
                └─────────────┘   └─────────────┘
```

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- Google API Key (for Gemini)
- Physics textbook database (SQLite)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ai-physics-tutor
   ```

2. **Create virtual environment**
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env and add your Google API key
   ```

5. **Run the application**
   ```bash
   streamlit run streamlit_app.py
   ```

## 🔧 Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# Required
GOOGLE_API_KEY=your_google_api_key_here

# Optional
DATABASE_PATH=physics_book.db
CHROMA_PERSIST_DIR=./chroma_db
EMBEDDING_MODEL=distiluse-base-multilingual-cased-v2
LLM_MODEL=gemini-pro
LLM_TEMPERATURE=0.3
CHUNK_SIZE=500
CHUNK_OVERLAP=50
RETRIEVAL_K=5
```

### Getting Google API Key

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the key to your `.env` file

## 📖 Usage

### Web Interface

1. **Initialize System**: Click "Initialize AI Tutor" in the sidebar
2. **Ask Questions**: Type your physics questions in the chat input
3. **Get Answers**: Receive detailed explanations with source references
4. **Continue Conversation**: The system remembers context for follow-up questions

### Example Questions

**English:**
- "What is Newton's first law of motion?"
- "Explain the concept of momentum"
- "How do you calculate kinetic energy?"
- "What is the difference between speed and velocity?"

**Bengali:**
- "নিউটনের প্রথম সূত্র কী?"
- "ভেক্টর কী?"
- "গতিশক্তি কীভাবে হিসাব করা হয়?"
- "বেগ এবং দ্রুতির মধ্যে পার্থক্য কী?"

### Programmatic Usage

```python
from ai_tutor import AITutor

# Initialize tutor
tutor = AITutor()
tutor.initialize_system()

# Ask a question
response = tutor.ask_question("What is Newton's second law?")
print(response["answer"])

# Get conversation history
history = tutor.get_conversation_history()

# Clear conversation
tutor.clear_conversation_history()
```

## 🏗️ System Components

### 1. AITutor Class (`ai_tutor.py`)

The core RAG system that handles:
- Data loading and preprocessing
- Text chunking and embedding
- Vector store management
- Question answering
- Conversation memory

### 2. Streamlit App (`streamlit_app.py`)

Web interface providing:
- Chat-like UI
- System initialization
- Real-time responses
- Conversation management
- Status monitoring

### 3. Text Processing

- **Cleaning**: Handles Bengali and English text with LaTeX
- **Chunking**: Splits text into manageable pieces
- **Embedding**: Converts text to vectors using SentenceTransformers

### 4. Vector Database

- **ChromaDB**: Persistent vector storage
- **Similarity Search**: Finds relevant content
- **Metadata**: Preserves source information

## 📊 Database Schema

The system expects a SQLite database with the following structure:

```sql
CREATE TABLE book_images (
    id INTEGER PRIMARY KEY,
    book_name TEXT,
    author_name TEXT,
    page_number INTEGER,
    text_content TEXT
);
```

## 🔧 Customization

### Adding New Subjects

1. Update the database with new content
2. Modify the prompt template in `setup_qa_chain()`
3. Adjust the system description

### Changing Models

**Embedding Model:**
```python
# In ai_tutor.py
self.embeddings = SentenceTransformerEmbeddings("your-model-name")
```

**LLM Model:**
```python
# In ai_tutor.py
self.llm = ChatGoogleGenerativeAI(
    model="gemini-pro-vision",  # or other models
    temperature=0.3
)
```

### UI Customization

Modify the CSS in `streamlit_app.py` to change:
- Color scheme
- Layout
- Message styling
- Sidebar content

## 🐛 Troubleshooting

### Common Issues

1. **API Key Error**
   - Ensure `GOOGLE_API_KEY` is set in `.env`
   - Check API key validity

2. **Database Not Found**
   - Verify `physics_book.db` exists
   - Check database path in configuration

3. **Memory Issues**
   - Reduce `CHUNK_SIZE` in configuration
   - Use smaller embedding models

4. **Slow Performance**
   - Reduce `RETRIEVAL_K` value
   - Use GPU acceleration if available

### Debug Mode

Enable debug logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **LangChain**: For the RAG framework
- **ChromaDB**: For vector storage
- **Google Gemini**: For the language model
- **Streamlit**: For the web interface
- **SentenceTransformers**: For embeddings

## 📞 Support

For questions or issues:
1. Check the troubleshooting section
2. Search existing issues
3. Create a new issue with details

---

**Happy Learning! 🎓**