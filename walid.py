import sqlite3
import os
from typing import List, Dict, Optional
from langchain.docstore.document import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter
from sentence_transformers import SentenceTransformer
from langchain_community.vectorstores import FAISS
from langchain_community.embeddings import HuggingFaceEmbeddings
import streamlit as st
from PIL import Image
import base64
from io import BytesIO

class BookSearchEngine:
    def __init__(self, db_path: str = "physics_book.db"):
        self.db_path = db_path
        self.documents = []
        self.chunks = []
        self.vector_store = None
        self.embeddings = None
        
    def load_documents_from_db(self) -> List[Document]:
        """Load documents from SQLite database and create LangChain Document objects"""
        print("Loading documents from database...")
        
        # Connect to SQLite database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Query all rows from book_images table
        cursor.execute("SELECT book_name, author_name, page_number, text_content FROM book_images")
        rows = cursor.fetchall()
        
        documents = []
        for row in rows:
            book_name, author_name, page_number, text_content = row
            
            # Create Document with text content and metadata
            doc = Document(
                page_content=text_content,
                metadata={
                    "book_name": book_name,
                    "author_name": author_name,
                    "page_number": page_number
                }
            )
            documents.append(doc)
        
        conn.close()
        print(f"Loaded {len(documents)} documents from database")
        self.documents = documents
        return documents
    
    def split_documents(self, chunk_size: int = 1000, chunk_overlap: int = 200) -> List[Document]:
        """Split documents into smaller chunks while preserving metadata"""
        print("Splitting documents into chunks...")
        
        if not self.documents:
            self.load_documents_from_db()
        
        # Initialize text splitter
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
        )
        
        # Split documents while preserving metadata
        chunks = text_splitter.split_documents(self.documents)
        
        print(f"Created {len(chunks)} chunks from {len(self.documents)} documents")
        self.chunks = chunks
        return chunks
    
    def create_embeddings(self, model_name: str = "paraphrase-multilingual-mpnet-base-v2"):
        """Initialize the multilingual embedding model"""
        print(f"Loading embedding model: {model_name}")
        
        # Initialize HuggingFace embeddings with multilingual model
        self.embeddings = HuggingFaceEmbeddings(
            model_name=model_name,
            model_kwargs={'device': 'cpu'},  # Use CPU for compatibility
            encode_kwargs={'normalize_embeddings': True}
        )
        
        print("Embedding model loaded successfully")
        return self.embeddings
    
    def create_vector_store(self):
        """Create FAISS vector store from text chunks"""
        print("Creating FAISS vector store...")
        
        if not self.chunks:
            self.split_documents()
        
        if not self.embeddings:
            self.create_embeddings()
        
        # Create FAISS vector store
        self.vector_store = FAISS.from_documents(
            documents=self.chunks,
            embedding=self.embeddings
        )
        
        print("FAISS vector store created successfully")
        return self.vector_store
    
    def search(self, query: str, k: int = 5) -> List[str]:
        """Search for relevant chunks and return unique page numbers"""
        if not self.vector_store:
            self.create_vector_store()
        
        # Perform similarity search
        results = self.vector_store.similarity_search(query, k=k)
        
        # Extract page numbers from metadata
        page_numbers = []
        for result in results:
            page_num = result.metadata.get("page_number")
            if page_num and page_num not in page_numbers:
                page_numbers.append(page_num)
        
        return page_numbers
    
    def search_with_context(self, query: str, k: int = 5) -> List[dict]:
        """Search and return results with context"""
        if not self.vector_store:
            self.create_vector_store()
        
        # Perform similarity search
        results = self.vector_store.similarity_search(query, k=k)
        
        # Format results with context
        formatted_results = []
        for result in results:
            formatted_results.append({
                "page_number": result.metadata.get("page_number"),
                "book_name": result.metadata.get("book_name"),
                "author_name": result.metadata.get("author_name"),
                "content": result.page_content[:200] + "..." if len(result.page_content) > 200 else result.page_content
            })
        
        return formatted_results

    def get_full_page_content(self, book_name: str, author_name: str, page_number: str) -> Optional[Dict]:
        """Retrieve complete page content including text and image path"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("""
            SELECT book_name, author_name, page_number, text_content, image_file_name, image_path
            FROM book_images
            WHERE book_name = ? AND author_name = ? AND page_number = ?
        """, (book_name, author_name, page_number))

        row = cursor.fetchone()
        conn.close()

        if row:
            return {
                "book_name": row[0],
                "author_name": row[1],
                "page_number": row[2],
                "text_content": row[3],
                "image_file_name": row[4],
                "image_path": row[5]
            }
        return None

    def get_rag_context(self, query: str, k: int = 3) -> Dict:
        """Get RAG-ready context with full page content for each result"""
        if not self.vector_store:
            self.create_vector_store()

        # Perform similarity search
        results = self.vector_store.similarity_search(query, k=k)

        rag_context = {
            "query": query,
            "pages": [],
            "context_text": "",
            "metadata": {
                "total_pages": 0,
                "books_referenced": set(),
                "authors": set()
            }
        }

        seen_pages = set()

        for result in results:
            page_key = (result.metadata.get("book_name"),
                       result.metadata.get("author_name"),
                       result.metadata.get("page_number"))

            if page_key not in seen_pages:
                seen_pages.add(page_key)

                # Get full page content
                full_page = self.get_full_page_content(
                    page_key[0], page_key[1], page_key[2]
                )

                if full_page:
                    rag_context["pages"].append(full_page)
                    rag_context["context_text"] += f"\n\n--- Page {full_page['page_number']} from {full_page['book_name']} by {full_page['author_name']} ---\n"
                    rag_context["context_text"] += full_page["text_content"]

                    rag_context["metadata"]["books_referenced"].add(full_page["book_name"])
                    rag_context["metadata"]["authors"].add(full_page["author_name"])

        rag_context["metadata"]["total_pages"] = len(rag_context["pages"])
        rag_context["metadata"]["books_referenced"] = list(rag_context["metadata"]["books_referenced"])
        rag_context["metadata"]["authors"] = list(rag_context["metadata"]["authors"])

        return rag_context

def display_page_content(page_data: Dict):
    """Display full page content in an expandable format"""
    with st.expander(f"📖 Full Page Content - Page {page_data['page_number']}", expanded=False):
        col1, col2 = st.columns([2, 1])

        with col1:
            st.subheader("📄 Text Content")
            st.text_area(
                "Full Page Text",
                value=page_data["text_content"],
                height=400,
                key=f"text_{page_data['page_number']}_{page_data['book_name']}"
            )

        with col2:
            st.subheader("📊 Page Information")
            st.write(f"**Book:** {page_data['book_name']}")
            st.write(f"**Author:** {page_data['author_name']}")
            st.write(f"**Page:** {page_data['page_number']}")
            st.write(f"**Text Length:** {len(page_data['text_content'])} characters")

            # Image information
            if page_data.get("image_path"):
                st.write(f"**Image File:** {page_data['image_file_name']}")

                # Try to display image if it exists
                image_path = page_data["image_path"]
                if os.path.exists(image_path):
                    try:
                        image = Image.open(image_path)
                        st.image(image, caption=f"Page {page_data['page_number']}", use_column_width=True)
                    except Exception as e:
                        st.warning(f"Could not load image: {e}")
                else:
                    st.warning("Image file not found in the specified path")

def main():
    st.title("📚 Book Search Engine")
    st.write("Search through your book content and find specific page numbers!")
    
    # Initialize search engine
    if 'search_engine' not in st.session_state:
        st.session_state.search_engine = BookSearchEngine()
    
    search_engine = st.session_state.search_engine
    
    # Initialize the system if not already done
    if not search_engine.vector_store:
        with st.spinner("Initializing search engine... This may take a few minutes on first run."):
            try:
                search_engine.load_documents_from_db()
                search_engine.split_documents()
                search_engine.create_embeddings()
                search_engine.create_vector_store()
                st.success("Search engine initialized successfully!")
            except Exception as e:
                st.error(f"Error initializing search engine: {str(e)}")
                return
    
    # Search interface
    query = st.text_input("Enter your search query:", placeholder="e.g., জৈব যৌগ")

    col1, col2, col3 = st.columns([1, 2, 2])
    with col1:
        search_button = st.button("🔍 Search", type="primary")
    with col2:
        show_context = st.checkbox("Show context", value=True)
    with col3:
        show_full_pages = st.checkbox("Show full pages", value=False)
    
    if search_button and query:
        with st.spinner("Searching..."):
            try:
                if show_context:
                    results = search_engine.search_with_context(query, k=10)
                    
                    if results:
                        st.success(f"Found {len(results)} relevant results!")
                        
                        # Extract unique page numbers
                        page_numbers = list(set([r["page_number"] for r in results if r["page_number"]]))
                        page_numbers.sort()
                        
                        st.subheader("📄 Page Numbers:")
                        st.write(", ".join(page_numbers))
                        
                        st.subheader("📖 Search Results with Context:")
                        for i, result in enumerate(results, 1):
                            with st.expander(f"Result {i} - Page {result['page_number']}", expanded=False):
                                st.write(f"**Book:** {result['book_name']}")
                                st.write(f"**Author:** {result['author_name']}")
                                st.write(f"**Page:** {result['page_number']}")
                                st.write(f"**Content:** {result['content']}")

                                # Add button to view full page
                                if st.button(f"📄 View Full Page {result['page_number']}",
                                           key=f"view_page_{i}_{result['page_number']}"):
                                    full_page = search_engine.get_full_page_content(
                                        result['book_name'],
                                        result['author_name'],
                                        result['page_number']
                                    )
                                    if full_page:
                                        st.session_state[f"show_page_{result['page_number']}"] = full_page

                                # Display full page if requested
                                if f"show_page_{result['page_number']}" in st.session_state:
                                    display_page_content(st.session_state[f"show_page_{result['page_number']}"])

                        # Show full pages option
                        if show_full_pages:
                            st.subheader("📚 Complete Page Contents:")
                            unique_pages = {}
                            for result in results:
                                page_key = f"{result['book_name']}_{result['author_name']}_{result['page_number']}"
                                if page_key not in unique_pages:
                                    unique_pages[page_key] = result

                            for page_key, result in unique_pages.items():
                                full_page = search_engine.get_full_page_content(
                                    result['book_name'],
                                    result['author_name'],
                                    result['page_number']
                                )
                                if full_page:
                                    display_page_content(full_page)
                    else:
                        st.warning("No results found for your query.")
                else:
                    page_numbers = search_engine.search(query, k=10)
                    
                    if page_numbers:
                        st.success(f"Found content on {len(page_numbers)} pages!")
                        st.subheader("📄 Page Numbers:")
                        st.write(", ".join(page_numbers))
                    else:
                        st.warning("No results found for your query.")

            except Exception as e:
                st.error(f"Error during search: {str(e)}")

    # RAG Context Section
    if query and search_button:
        st.markdown("---")
        st.subheader("🤖 RAG Context (For AI Integration)")

        with st.expander("View RAG-Ready Context", expanded=False):
            try:
                rag_context = search_engine.get_rag_context(query, k=3)

                st.write("**Context Summary:**")
                st.write(f"- Total Pages: {rag_context['metadata']['total_pages']}")
                st.write(f"- Books Referenced: {', '.join(rag_context['metadata']['books_referenced'])}")
                st.write(f"- Authors: <AUTHORS>

                st.write("**Full Context Text:**")
                st.text_area(
                    "RAG Context (Copy this for AI models)",
                    value=rag_context["context_text"],
                    height=300,
                    help="This is the complete context that can be used with RAG systems"
                )

                # Download context as text file
                if st.button("💾 Download RAG Context"):
                    context_filename = f"rag_context_{query.replace(' ', '_')}.txt"
                    st.download_button(
                        label="📥 Download Context File",
                        data=rag_context["context_text"],
                        file_name=context_filename,
                        mime="text/plain"
                    )

            except Exception as e:
                st.error(f"Error generating RAG context: {str(e)}")

if __name__ == "__main__":
    main()
